use anyhow::Result;
use axum::{Router, extract::State, http::StatusCode, response::Json, routing::get};
use std::sync::Arc;
use tokio::net::TcpListener;
use tracing::{error, info, warn};
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};
use wellbot_bridge::{
    config::Config,
    services::{bridge_service::BridgeService, health_checker},
};

/// Application state for HTTP handlers
#[derive(Debug, Clone)]
struct AppState {
    bridge_service: Arc<BridgeService>,
}

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize logging
    init_logging().await?;

    // Load configuration
    let config = Config::from_env()?;

    // Initialize the bridge service
    let bridge_service = Arc::new(BridgeService::new(config.clone()).await?);

    // Create application state
    let app_state = AppState {
        bridge_service: bridge_service.clone(),
    };

    // Start health check HTTP server
    let health_port = config.health.port;
    let health_app = create_health_app(app_state.clone());

    tokio::spawn(async move {
        let listener = match TcpListener::bind(format!("0.0.0.0:{}", health_port)).await {
            Ok(listener) => {
                info!("🏥 Health check server listening on port {}", health_port);
                listener
            }
            Err(e) => {
                error!("Failed to bind health check server: {}", e);
                return;
            }
        };

        if let Err(e) = axum::serve(listener, health_app).await {
            error!("Health check server error: {}", e);
        }
    });

    info!(
        "🚀 Starting Wellbot Bridge Service v{}",
        env!("CARGO_PKG_VERSION")
    );

    Ok(())
}

/// Create health check HTTP application
fn create_health_app(state: AppState) -> Router {
    Router::new()
        .route("/health", get(health_handler))
        .route("/health/ready", get(readiness_handler))
        .route("/health/live", get(liveness_handler))
        .with_state(state)
}

/// Health check endpoint handler
async fn health_handler(State(state): State<AppState>) -> Result<Json<ServiceHealth>, StatusCode> {
    match state
        .bridge_service
        .health_checker()
        .perform_health_check()
        .await
    {
        Ok(health) => Ok(Json(health)),
        Err(e) => {
            warn!("Health check failed: {}", e);
            Err(StatusCode::SERVICE_UNAVAILABLE)
        }
    }
}

/// Readiness check endpoint handler
async fn readiness_handler(State(state): State<AppState>) -> StatusCode {
    let health = state
        .bridge_service
        .health_checker()
        .get_current_health()
        .await;

    match health.status {
        health_checker::HealthStatus::Healthy => StatusCode::OK,
        _ => StatusCode::SERVICE_UNAVAILABLE,
    }
}

/// Liveness check endpoint handler
async fn liveness_handler() -> StatusCode {
    // Simple liveness check - if we can respond, we're alive
    StatusCode::OK
}

/// Initialize structured logging based on configuration
async fn init_logging() -> Result<()> {
    // Try to load config for logging settings, but use defaults if it fails
    let log_level = std::env::var("LOG_LEVEL").unwrap_or_else(|_| "info".to_string());
    let log_format = std::env::var("LOG_FORMAT").unwrap_or_else(|_| "pretty".to_string());

    let env_filter = format!("wellbot_bridge={},tower_http=debug", log_level);

    let subscriber = tracing_subscriber::registry().with(
        tracing_subscriber::EnvFilter::try_from_default_env().unwrap_or_else(|_| env_filter.into()),
    );

    match log_format.as_str() {
        "json" => {
            subscriber
                .with(tracing_subscriber::fmt::layer().json().with_target(false))
                .init();
        }
        _ => {
            subscriber
                .with(tracing_subscriber::fmt::layer().with_target(false))
                .init();
        }
    }

    info!(
        "📝 Logging initialized - Level: {}, Format: {}",
        log_level, log_format
    );
    Ok(())
}

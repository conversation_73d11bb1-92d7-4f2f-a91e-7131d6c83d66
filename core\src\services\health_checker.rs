/*!
# Health Checker

Monitors the health of all service components and provides health status.
*/

use crate::{
    config::HealthConfig,
    error::BridgeResult,
    services::{
        ai_client::AiClient, chat_port_client::ChatPortClient, message_processor::MessageProcessor,
    },
};
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::{sync::Arc, time::Duration};
use tokio::time::interval;
use tracing::{debug, error, info, instrument};

/// Health status for individual components
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum HealthStatus {
    Healthy,
    Unhealthy,
    Unknown,
}

/// Health check result for a component
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComponentHealth {
    pub name: String,
    pub status: HealthStatus,
    pub last_check: DateTime<Utc>,
    pub error_message: Option<String>,
    pub response_time_ms: Option<u64>,
}

/// Overall service health
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>ize, Deserialize)]
pub struct ServiceHealth {
    pub service_name: String,
    pub version: String,
    pub status: HealthStatus,
    pub timestamp: DateTime<Utc>,
    pub uptime_seconds: u64,
    pub components: Vec<ComponentHealth>,
    pub processing_stats: Option<ProcessingStats>,
}

/// Processing statistics for health reporting
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProcessingStats {
    pub cached_messages: usize,
    pub available_permits: usize,
    pub max_concurrent: usize,
    pub queue_utilization_percent: f64,
}

/// Health checker service
#[derive(Debug)]
pub struct HealthChecker {
    config: HealthConfig,
    ai_client: Arc<AiClient>,
    chat_port_client: Arc<ChatPortClient>,
    message_processor: Option<Arc<MessageProcessor>>,
    start_time: DateTime<Utc>,
    last_health_check: Arc<tokio::sync::RwLock<ServiceHealth>>,
}

impl HealthChecker {
    /// Create a new health checker
    pub fn new(
        config: HealthConfig,
        ai_client: Arc<AiClient>,
        chat_port_client: Arc<ChatPortClient>,
    ) -> Self {
        let start_time = Utc::now();
        let initial_health = ServiceHealth {
            service_name: "wellbot-bridge".to_string(),
            version: env!("CARGO_PKG_VERSION").to_string(),
            status: HealthStatus::Unknown,
            timestamp: start_time,
            uptime_seconds: 0,
            components: vec![],
            processing_stats: None,
        };

        Self {
            config,
            ai_client,
            chat_port_client,
            message_processor: None,
            start_time,
            last_health_check: Arc::new(tokio::sync::RwLock::new(initial_health)),
        }
    }

    /// Set message processor for health monitoring
    pub fn set_message_processor(&mut self, processor: Arc<MessageProcessor>) {
        self.message_processor = Some(processor);
    }

    /// Start periodic health checks
    #[instrument(skip(self))]
    pub async fn start_periodic_checks(&self) -> BridgeResult<()> {
        let mut check_interval = interval(Duration::from_secs(self.config.check_interval_secs));

        info!(
            "Starting periodic health checks every {}s",
            self.config.check_interval_secs
        );

        loop {
            check_interval.tick().await;

            if let Err(e) = self.perform_health_check().await {
                error!("Health check failed: {}", e);
            }
        }
    }

    /// Perform a comprehensive health check
    #[instrument(skip(self))]
    pub async fn perform_health_check(&self) -> BridgeResult<ServiceHealth> {
        debug!("Performing health check");

        let mut components = Vec::new();

        // Check AI service health
        let ai_health = self.check_ai_service().await;
        components.push(ai_health);

        // Check chat-port service health
        let chat_port_health = self.check_chat_port_service().await;
        components.push(chat_port_health);

        // Get processing stats if available
        let processing_stats = if let Some(processor) = &self.message_processor {
            let stats = processor.get_stats().await;
            Some(ProcessingStats {
                cached_messages: stats.cached_messages,
                available_permits: stats.available_permits,
                max_concurrent: stats.max_concurrent,
                queue_utilization_percent: ((stats.max_concurrent - stats.available_permits)
                    as f64
                    / stats.max_concurrent as f64)
                    * 100.0,
            })
        } else {
            None
        };

        // Determine overall health status
        let overall_status = if components
            .iter()
            .all(|c| matches!(c.status, HealthStatus::Healthy))
        {
            HealthStatus::Healthy
        } else if components
            .iter()
            .any(|c| matches!(c.status, HealthStatus::Unhealthy))
        {
            HealthStatus::Unhealthy
        } else {
            HealthStatus::Unknown
        };

        let health = ServiceHealth {
            service_name: "wellbot-bridge".to_string(),
            version: env!("CARGO_PKG_VERSION").to_string(),
            status: overall_status,
            timestamp: Utc::now(),
            uptime_seconds: (Utc::now() - self.start_time).num_seconds() as u64,
            components,
            processing_stats,
        };

        // Update cached health status
        {
            let mut cached_health = self.last_health_check.write().await;
            *cached_health = health.clone();
        }

        debug!("Health check completed - Status: {:?}", health.status);
        Ok(health)
    }

    /// Check AI service health
    async fn check_ai_service(&self) -> ComponentHealth {
        let start_time = std::time::Instant::now();

        match tokio::time::timeout(
            Duration::from_secs(self.config.timeout_secs),
            self.ai_client.health_check(),
        )
        .await
        {
            Ok(Ok(is_healthy)) => ComponentHealth {
                name: "ai_service".to_string(),
                status: if is_healthy {
                    HealthStatus::Healthy
                } else {
                    HealthStatus::Unhealthy
                },
                last_check: Utc::now(),
                error_message: if is_healthy {
                    None
                } else {
                    Some("AI service health check failed".to_string())
                },
                response_time_ms: Some(start_time.elapsed().as_millis() as u64),
            },
            Ok(Err(e)) => ComponentHealth {
                name: "ai_service".to_string(),
                status: HealthStatus::Unhealthy,
                last_check: Utc::now(),
                error_message: Some(e.to_string()),
                response_time_ms: Some(start_time.elapsed().as_millis() as u64),
            },
            Err(_) => ComponentHealth {
                name: "ai_service".to_string(),
                status: HealthStatus::Unhealthy,
                last_check: Utc::now(),
                error_message: Some("AI service health check timeout".to_string()),
                response_time_ms: Some(self.config.timeout_secs * 1000),
            },
        }
    }

    /// Check chat-port service health
    async fn check_chat_port_service(&self) -> ComponentHealth {
        let start_time = std::time::Instant::now();

        match tokio::time::timeout(
            Duration::from_secs(self.config.timeout_secs),
            self.chat_port_client.health_check(),
        )
        .await
        {
            Ok(Ok(is_healthy)) => ComponentHealth {
                name: "chat_port".to_string(),
                status: if is_healthy {
                    HealthStatus::Healthy
                } else {
                    HealthStatus::Unhealthy
                },
                last_check: Utc::now(),
                error_message: if is_healthy {
                    None
                } else {
                    Some("Chat-port service health check failed".to_string())
                },
                response_time_ms: Some(start_time.elapsed().as_millis() as u64),
            },
            Ok(Err(e)) => ComponentHealth {
                name: "chat_port".to_string(),
                status: HealthStatus::Unhealthy,
                last_check: Utc::now(),
                error_message: Some(e.to_string()),
                response_time_ms: Some(start_time.elapsed().as_millis() as u64),
            },
            Err(_) => ComponentHealth {
                name: "chat_port".to_string(),
                status: HealthStatus::Unhealthy,
                last_check: Utc::now(),
                error_message: Some("Chat-port service health check timeout".to_string()),
                response_time_ms: Some(self.config.timeout_secs * 1000),
            },
        }
    }

    /// Get current health status (cached)
    pub async fn get_current_health(&self) -> ServiceHealth {
        self.last_health_check.read().await.clone()
    }

    /// Check if service is healthy
    pub async fn is_healthy(&self) -> bool {
        let health = self.get_current_health().await;
        matches!(health.status, HealthStatus::Healthy)
    }

    /// Get health configuration
    pub fn config(&self) -> &HealthConfig {
        &self.config
    }
}
